{"name": "guidia-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@azure/storage-blob": "^12.26.0", "@hookform/resolvers": "^5.0.1", "@radix-ui/react-avatar": "^1.1.6", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-dialog": "^1.1.7", "@radix-ui/react-dropdown-menu": "^2.1.11", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-popover": "^1.1.7", "@radix-ui/react-progress": "^1.1.3", "@radix-ui/react-scroll-area": "^1.2.4", "@radix-ui/react-select": "^2.1.7", "@radix-ui/react-separator": "^1.1.3", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.1.4", "@radix-ui/react-tabs": "^1.1.4", "@radix-ui/react-toggle": "^1.1.3", "@radix-ui/react-tooltip": "^1.2.3", "@react-pdf/renderer": "^4.3.0", "@sentry/react": "^9.5.0", "@sentry/vite-plugin": "^3.2.2", "@shadcn/ui": "^0.0.4", "@types/node": "^22.10.1", "@types/quill": "^2.0.14", "@types/react-helmet": "^6.1.11", "@types/turndown": "^5.0.5", "axios": "^1.8.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "dompurify": "^3.2.4", "dotenv": "^16.5.0", "file-saver": "^2.0.5", "firebase": "^11.6.0", "framer-motion": "^10.18.0", "highlight.js": "^11.11.1", "lucide-react": "^0.453.0", "path": "^0.12.7", "quill": "^2.0.3", "react": "^18.3.1", "react-csv-downloader": "^3.3.0", "react-day-picker": "^9.6.6", "react-dom": "^18.3.1", "react-helmet": "^6.1.0", "react-hook-form": "^7.55.0", "react-markdown": "^10.1.0", "react-pdf": "^9.2.1", "react-pro-sidebar": "^1.1.0", "react-quilljs": "^2.0.5", "react-router-dom": "^7.0.1", "recharts": "^2.15.2", "remark-gfm": "^4.0.1", "socket.io-client": "^4.8.1", "sonner": "^2.0.1", "styled-components": "^6.1.17", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "turndown": "^7.2.0", "vaul": "^1.1.2", "xlsx": "^0.18.5", "zod": "^3.24.2"}, "devDependencies": {"@types/axios": "^0.9.36", "@types/react": "^18.3.6", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.20", "postcss": "^8.4.47", "tailwindcss": "^3.4.14", "typescript": "^5.5.3", "vite": "^6.2.1"}}
{"name": "guidia-auth", "version": "1.0.0", "main": "index.js", "scripts": {"start": "node index.js", "dev": "node --no-deprecation index.js", "test-firebase": "node scripts/test-firebase-connection.js", "rotate-key": "node scripts/rotate-firebase-key.js", "schedule-rotation": "node scripts/schedule-key-rotation.js", "test-db": "node tests/test-db-connection.js", "test-db-context": "node tests/test-db-context.js", "update-db-config": "node scripts/update-db-config.js"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@azure/storage-blob": "^12.26.0", "axios": "^1.6.7", "bcrypt": "^5.1.1", "cors": "^2.8.5", "dompurify": "^3.2.5", "dotenv": "^16.5.0", "express": "^4.21.2", "express-rate-limit": "^7.5.0", "firebase-admin": "^13.2.0", "helmet": "^7.2.0", "hpp": "^0.2.3", "ipaddr.js": "^2.2.0", "jsdom": "^24.1.3", "jsonwebtoken": "^9.0.2", "moment": "^2.30.1", "multer": "^1.4.5-lts.1", "mysql2": "^3.13.0", "node-schedule": "^2.1.1", "nodemailer": "^6.10.0", "openai": "^4.95.1", "punycode": "^2.3.1", "socket.io": "^4.8.1", "validator": "^13.15.0", "xss": "^1.0.15"}, "devDependencies": {"nodemon": "^3.0.3"}}
{"name": "guidia", "private": true, "version": "0.0.0", "scripts": {"dev:frontend": "cd frontend && npm run dev", "dev:backend": "cd backend && npm run dev", "dev": "concurrently \"npm run dev:frontend\" \"npm run dev:backend\"", "install:frontend": "cd frontend && npm install", "install:backend": "cd backend && npm install", "install:all": "concurrently \"npm run install:frontend\" \"npm run install:backend\"", "build:frontend": "cd frontend && npm run build", "build:backend": "cd backend && npm run build", "build": "npm run build:frontend && npm run build:backend", "lint": "cd frontend && npm run lint"}, "devDependencies": {"concurrently": "^9.1.2"}}
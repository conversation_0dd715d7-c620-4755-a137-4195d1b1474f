# typical gitignore for web apps
node_modules
**/node_modules
dist
.DS_Store
# Sentry Config File
.env.sentry-build-plugin
**/env.sentry-build-plugin

# Package lock files (if you want to exclude them)
package-lock.json
**/package-lock.json

# Large mock data files
frontend/src/mocks/*
**/mock-data/*

# Environment files
.env
**/.env
.env.local
.env.development
.env.production

# Build output
build/
dist/
out/
**/build/
**/dist/
**/out/

# Logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
**/logs/

# IDE specific files
.idea/
.vscode/
*.swp
*.swo

#examples
index.sql
# Firebase service account keys
backend/firebase-service-account.json
**/firebase-service-account.json
**/key-backups/
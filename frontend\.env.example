# Database configuration
DB_HOST=localhost
DB_USER=your_db_user
DB_PASSWORD=your_db_password
DB_NAME=guidia-web-db
DB_SSL=false

# Server configuration
PORT=3001
NODE_ENV=development # Use 'production' in production environment

# Security configuration
JWT_SECRET=your_jwt_secret_key # Use a strong random string at least 32 characters long
JWT_EXPIRY=1h # Token expiration time
JWT_REFRESH_EXPIRY=7d # Refresh token expiration time
ENCRYPTION_KEY=your_encryption_key # Use a strong random string at least 32 characters long
CSRF_SECRET=your_csrf_secret # Use a strong random string at least 32 characters long

# Email configuration
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your_email_password

# Frontend configuration
VITE_API_BASE_URL=http://localhost:3001
VITE_USE_AZURE_FALLBACK=false
FRONTEND_URL=http://localhost:1030

# Azure Storage configuration
AZURE_STORAGE_CONNECTION_STRING=your_azure_storage_connection_string
AZURE_STORAGE_CONTAINER_NAME=your_azure_container_name

# Firebase configuration
VITE_FIREBASE_DATABASE_URL=your_firebase_database_url
VITE_FIREBASE_API_KEY=your_firebase_api_key
VITE_FIREBASE_AUTH_DOMAIN=your_firebase_auth_domain
VITE_FIREBASE_PROJECT_ID=your_firebase_project_id
VITE_FIREBASE_STORAGE_BUCKET=your_firebase_storage_bucket
VITE_FIREBASE_MESSAGING_SENDER_ID=your_firebase_messaging_sender_id
VITE_FIREBASE_APP_ID=your_firebase_app_id
VITE_FIREBASE_MEASUREMENT_ID=your_firebase_measurement_id

# AI configuration
SAMBANOVA_API_KEY=your_sambanova_api_key
DEEPSEEK_API_KEY=your_deepseek_api_key
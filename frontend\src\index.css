@import url('https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,100..900;1,100..900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Open+Sans:ital,wght@0,300..800;1,300..800&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Add smooth transitions for theme changes */
* {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

/* Global theme overrides to ensure consistent dark mode */
@layer utilities {
  /* Text colors */
  .text-gray-50 { @apply text-neutral-50; }
  .text-gray-100 { @apply text-neutral-100; }
  .text-gray-200 { @apply text-neutral-200; }
  .text-gray-300 { @apply text-neutral-300; }
  .text-gray-400 { @apply text-muted-foreground; }
  .text-gray-500 { @apply text-muted-foreground; }
  .text-gray-600 { @apply text-muted-foreground; }
  .text-gray-700 { @apply text-foreground; }
  .text-gray-800 { @apply text-foreground; }
  .text-gray-900 { @apply text-foreground; }

  /* Background colors */
  .bg-white { @apply bg-card; }
  .bg-gray-50 { @apply bg-secondary; }
  .bg-gray-100 { @apply bg-secondary-light; }
  .bg-gray-200 { @apply bg-secondary-dark; }
  .bg-gray-300 { @apply bg-secondary-dark; }
  .bg-gray-400 { @apply bg-secondary-dark; }
  .bg-gray-500 { @apply bg-muted; }

  /* Border colors */
  .border-gray-100 { @apply border-border; }
  .border-gray-200 { @apply border-border; }
  .border-gray-300 { @apply border-border; }
  .border-gray-400 { @apply border-border; }
  .border-gray-500 { @apply border-border; }

  /* Hover states */
  .hover\:bg-gray-50 { @apply hover:bg-secondary; }
  .hover\:bg-gray-100 { @apply hover:bg-secondary-light; }
  .hover\:bg-gray-200 { @apply hover:bg-secondary-dark; }

  .hover\:text-gray-200 { @apply hover:text-foreground; }
  .hover\:text-gray-500 { @apply hover:text-foreground; }
  .hover\:text-gray-600 { @apply hover:text-foreground; }
  .hover\:text-gray-700 { @apply hover:text-foreground; }
  .hover\:text-gray-800 { @apply hover:text-foreground; }

  .hover\:border-gray-300 { @apply hover:border-border; }
}

@font-face {
  font-family: 'Grillmaster Extended';
  src: url('/fonts/fonnts.com-grillmaster_extended_004_regular.otf') format('opentype');
  font-weight: normal;
  font-style: normal;
}

@layer utilities {
  /* Add ripple animation for checkbox */
  @keyframes ripple {
    to {
      transform: scale(4);
      opacity: 0;
    }
  }

  .animate-ripple {
    animation: ripple 0.6s linear forwards;
  }

  .text-balance {
    text-wrap: balance;
  }

  /* Adaptive text color that switches between dark and light based on theme */
  .text-adaptive-dark {
    @apply text-foreground;
  }
}

@layer base {
  :root {
    /* Base Colors */
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --radius: 0.5rem;

    /* Brand Color Palette - Light Mode */
    --brand: 345 100% 25%; /* #800020 - Main burgundy */
    --brand-light: 345 68% 37%; /* #A01F3D */
    --brand-lighter: 345 53% 49%; /* #C03C59 */
    --brand-dark: 345 70% 30%; /* #9f1239 - rose-800 */
    --brand-darker: 345 100% 13%; /* #400010 */

    /* Secondary Color Palette - Light Mode (Neutral Grays) */
    --secondary: 0 0% 97%; /* #F7F7F7 - Very light gray */
    --secondary-light: 0 0% 94%; /* #EFEFEF - Slightly darker gray */
    --secondary-lighter: 0 0% 91%; /* #E8E8E8 - Another subtle gray */
    --secondary-dark: 0 0% 83%; /* #D3D3D3 - Medium light gray */
    --secondary-darker: 0 0% 75%; /* #BFBFBF - Medium gray */

    /* Neutral Colors - Light Mode */
    --neutral-50: 0 0% 100%; /* #FFFFFF */
    --neutral-100: 0 0% 98%; /* #F9F9F9 */
    --neutral-200: 220 13% 91%; /* #E5E7EB */
    --neutral-300: 216 12% 84%; /* #D1D5DB */
    --neutral-400: 218 11% 65%; /* #9CA3AF */
    --neutral-500: 220 9% 46%; /* #6B7280 */
    --neutral-600: 215 14% 34%; /* #4B5563 */
    --neutral-700: 217 19% 27%; /* #374151 */
    --neutral-800: 215 28% 17%; /* #1F2937 */
    --neutral-900: 221 39% 11%; /* #111827 */

    /* Accent Colors - Light Mode */
    --accent-gold: 43 77% 46%; /* #D4A017 */
    --accent-indigo: 244 61% 59%; /* #4F46E5 */
    --accent-purple: 258 90% 66%; /* #8B5CF6 */

    /* Functional Colors - Light Mode */
    --success: 158 64% 40%; /* #10B981 */
    --warning: 38 92% 50%; /* #F59E0B */
    --error: 0 84% 60%; /* #EF4444 */
    --info: 217 91% 60%; /* #3B82F6 */

    /* System Colors (Shadcn compatibility) */
    --primary: var(--brand);
    --primary-foreground: 0 0% 98%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: var(--brand);

    /* Chart Colors */
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;

    /* Sidebar Colors */
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: var(--brand);
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: var(--brand);
  }

  .dark {
    /* Base Colors */
    --background: 0 0% 7%; /* #121212 */
    --foreground: 0 0% 98%; /* #F9FAFB */
    --card: 0 0% 12%; /* #1E1E1E */
    --card-foreground: 0 0% 98%; /* #F9FAFB */
    --popover: 0 0% 12%; /* #1E1E1E */
    --popover-foreground: 0 0% 98%; /* #F9FAFB */

    /* Brand Color Palette - Dark Mode (Same as Light Mode) */
    --brand: 345 100% 25%; /* #800020 - Main burgundy */
    --brand-light: 345 68% 37%; /* #A01F3D */
    --brand-lighter: 345 53% 49%; /* #C03C59 */
    --brand-dark: 345 70% 30%; /* #9f1239 - rose-800 */
    --brand-darker: 345 100% 13%; /* #400010 */

    /* Secondary Color Palette - Dark Mode (Neutral Grays) */
    --secondary: 0 0% 15%; /* #262626 - Dark gray for dark mode */
    --secondary-light: 0 0% 18%; /* #2E2E2E - Slightly lighter gray */
    --secondary-lighter: 0 0% 22%; /* #383838 - Another subtle dark gray */
    --secondary-dark: 0 0% 12%; /* #1F1F1F - Darker gray */
    --secondary-darker: 0 0% 9%; /* #171717 - Very dark gray */

    /* Neutral Colors - Dark Mode */
    --neutral-50: 0 0% 98%; /* #F9FAFB */
    --neutral-100: 0 0% 92%; /* #EBEDF0 */
    --neutral-200: 0 0% 84%; /* #D1D5DB */
    --neutral-300: 0 0% 69%; /* #B0B8C1 */
    --neutral-400: 0 0% 75%; /* Increased from 56% for better contrast */
    --neutral-500: 0 0% 65%; /* Increased from 45% for better contrast */
    --neutral-600: 0 0% 32%; /* #525252 */
    --neutral-700: 0 0% 25%; /* #404040 */
    --neutral-800: 0 0% 18%; /* #2E2E2E */
    --neutral-900: 0 0% 12%; /* #1E1E1E */

    /* Accent Colors - Dark Mode */
    --accent-gold: 43 90% 63%; /* #F7C94A */
    --accent-indigo: 244 100% 74%; /* #818CF8 */
    --accent-purple: 258 90% 76%; /* #A78BFA */

    /* Functional Colors - Dark Mode */
    --success: 158 84% 52%; /* #34D399 */
    --warning: 38 92% 60%; /* #FBBF24 */
    --error: 0 91% 71%; /* #F87171 */
    --info: 217 91% 67%; /* #60A5FA */

    /* System Colors (Shadcn compatibility) */
    --primary: var(--brand);
    --primary-foreground: 0 0% 9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 80%; /* Increased from 63.9% for better contrast */
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 100%; /* Increased from 98% for better contrast */
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: var(--brand);

    /* Chart Colors */
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;

    /* Sidebar Colors */
    --sidebar-background: 0 0% 12%; /* #1E1E1E */
    --sidebar-foreground: 0 0% 98%; /* #F9FAFB */
    --sidebar-primary: 345 62% 65%; /* Brighter burgundy for better visibility in dark mode */
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 0 0% 18%; /* #2E2E2E */
    --sidebar-accent-foreground: 0 0% 98%;
    --sidebar-border: 0 0% 22%; /* Slightly lighter border for better visibility */
    --sidebar-ring: 345 62% 65%;
  }

  html {
    font-family: 'Open Sans', system-ui, sans-serif;
    scroll-behavior: smooth;
  }

  h1, h2, h3, h4, h5, h6, button, .font-montserrat {
    font-family: 'Montserrat', sans-serif;
  }
}

body {
  margin: 0;
  min-width: 320px;
  min-height: 100vh;
  @apply bg-background text-foreground;
}

@layer base {
  * {
    @apply border-border;
  }
}

/* Hide scrollbar for Chrome, Safari and Edge */
::-webkit-scrollbar {
  display: none;
}

/* Hide scrollbar for Firefox */
html {
  scrollbar-width: none;
}

/* Sidebar animation keyframes */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Custom scrollbar styles */
.custom-scrollbar {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}
.custom-scrollbar::-webkit-scrollbar {
  display: none;  /* Chrome, Safari and Opera */
}

/* Message content styles */
.message-content {
  word-wrap: break-word !important;
  overflow-wrap: break-word !important;
  word-break: break-word !important;
  max-width: 100%;
  overflow: hidden;
  hyphens: auto;
  white-space: normal !important;
}

/* Chat list styles */
.chat-list-item p {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  max-width: 100%;
  display: inline-block;
}

/* Chat message styles */
.chat-message-bubble {
  max-width: 80%;
  word-wrap: break-word;
  overflow-wrap: break-word;
  word-break: break-word;
}

/* Profile dropdown styles */
.profile-dropdown-name {
  max-width: 180px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Rich Text Editor styles */
.ql-container {
  font-family: inherit;
  font-size: inherit;
  border: none !important;
  position: relative !important;
  background-color: white !important;
  border-radius: 6px;
  z-index: 1 !important;
}

.dark .ql-container {
  background-color: #1e1e1e !important;
}

/* Fix for Quill container to ensure proper stacking */
.ql-container.ql-snow {
  overflow: visible !important;
}

.ql-toolbar {
  border: none !important;
  border-bottom: 1px solid #e5e7eb !important;
  background-color: rgba(255, 255, 255, 0.95) !important;
  position: relative !important;
  z-index: 100 !important;
  min-height: 42px !important;
  display: flex !important;
  align-items: center !important;
}

/* Hide the "Input" field that appears in the toolbar */
.ql-toolbar .ql-formats:first-child::before,
.ql-toolbar::before,
.ql-toolbar .ql-formats::before {
  content: none !important;
  display: none !important;
}

/* Remove any "Input" text that might be added by Quill */
.ql-toolbar .ql-formats:first-child label,
.ql-toolbar label {
  display: none !important;
}

/* Ensure proper spacing between toolbar items */
.ql-toolbar .ql-formats {
  margin-right: 10px !important;
}

/* Specific styles for our custom rich text editor */
.guidia-rich-text-editor .ql-toolbar::before,
.guidia-rich-text-editor .ql-toolbar .ql-formats::before {
  content: none !important;
  display: none !important;
}

.guidia-rich-text-editor .ql-toolbar input,
.guidia-rich-text-editor .ql-toolbar label {
  display: none !important;
}

/* Fix for header dropdown in our custom editor */
.guidia-rich-text-editor .ql-header .ql-picker-label {
  padding-right: 15px !important;
  white-space: nowrap !important;
  min-width: 100px !important;
}

.guidia-rich-text-editor .ql-header .ql-picker-label span {
  display: none !important;
}

.guidia-rich-text-editor .ql-header .ql-picker-label::before {
  content: "Normal" !important;
  display: inline-block !important;
  padding-right: 10px !important;
  white-space: nowrap !important;
}

.guidia-rich-text-editor .ql-header .ql-picker-label[data-value="1"]::before {
  content: "Heading 1" !important;
  white-space: nowrap !important;
}

.guidia-rich-text-editor .ql-header .ql-picker-label[data-value="2"]::before {
  content: "Heading 2" !important;
  white-space: nowrap !important;
}

.guidia-rich-text-editor .ql-header .ql-picker-label[data-value="3"]::before {
  content: "Heading 3" !important;
  white-space: nowrap !important;
}

.dark .ql-toolbar {
  background-color: rgba(30, 30, 30, 0.95) !important;
  border-bottom: 1px solid #333 !important;
}

/* Create proper stacking context for toolbar */
.ql-toolbar.ql-snow {
  display: flex !important;
  flex-wrap: wrap !important;
  align-items: center !important;
}

/* Ensure dropdowns appear above content */
.ql-picker-options {
  z-index: 9999 !important;
  position: absolute !important;
}

.ql-editor {
  padding: 0.75rem 1rem;
  min-height: 80px;
  max-height: 200px;
  overflow-y: auto;
  background-color: transparent !important;
  z-index: 100 !important;
  position: relative !important;
  cursor: text !important;
  outline: none !important;
  user-select: text !important;
  -webkit-user-select: text !important;
  -moz-user-select: text !important;
  -ms-user-select: text !important;
  caret-color: #800020 !important; /* Brand color for cursor */
}

/* Remove Quill branding/link */
.ql-editor p a[href="https://quilljs.com"],
.ql-editor a[href="https://quilljs.com"] {
  display: none !important;
}

.ql-editor p:has(a[href="https://quilljs.com"]) {
  display: none !important;
}

/* Quill list styles */
.ql-editor ul {
  list-style-type: disc !important;
  padding-left: 1.5rem !important;
  margin: 0.5rem 0 !important;
}

.ql-editor ol {
  list-style-type: decimal !important;
  padding-left: 1.5rem !important;
  margin: 0.5rem 0 !important;
}

.ql-editor li {
  margin: 0.25rem 0 !important;
  display: list-item !important;
  padding-left: 0 !important;
}

.ql-editor li::before {
  display: none !important;
}

/* Fix for Quill list rendering in messages */
.prose ul > li, .prose ol > li {
  padding-left: 0 !important;
  margin-left: 1.5rem !important;
}

.prose ul > li::before, .prose ol > li::before {
  display: none !important;
}

.prose ul {
  list-style-type: disc !important;
  padding-left: 0 !important;
  margin: 0.5rem 0 !important;
}

.prose ol {
  list-style-type: decimal !important;
  padding-left: 0 !important;
  margin: 0.5rem 0 !important;
}

/* Fix for user message content */
.user-message-content ul, .user-message-content ol {
  margin-top: 0.5rem !important;
  margin-bottom: 0.5rem !important;
  padding-left: 1.5rem !important;
  list-style-position: outside !important;
}

.user-message-content ul li, .user-message-content ol li {
  display: list-item !important;
  margin-left: 0 !important;
  padding-left: 0.5rem !important;
}

.user-message-content ul {
  list-style-type: disc !important;
}

.user-message-content ol {
  list-style-type: decimal !important;
}

/* Override Tailwind prose styles for user messages */
.prose .user-message-content ul > li::before,
.prose .user-message-content ol > li::before {
  display: none !important;
}

.prose .user-message-content ul > li,
.prose .user-message-content ol > li {
  padding-left: 0 !important;
}

/* Direct overrides for Tailwind prose variables */
.prose.user-message-content {
  --tw-prose-bullets: currentColor !important;
  --tw-prose-counters: currentColor !important;
}

/* Quill specific overrides */
.user-message-content .ql-editor ul,
.user-message-content ul.ql-bullet,
.user-message-content ul.ql-list {
  padding-left: 1.5rem !important;
  list-style-type: disc !important;
}

.user-message-content .ql-editor ol,
.user-message-content ol.ql-numbered,
.user-message-content ol.ql-list {
  padding-left: 1.5rem !important;
  list-style-type: decimal !important;
}

.user-message-content .ql-editor li,
.user-message-content li.ql-list-item {
  display: list-item !important;
  padding-left: 0 !important;
}

/* Force list styles for Quill content */
.user-message-content ul,
.user-message-content ol {
  list-style-position: outside !important;
}

.user-message-content li {
  display: list-item !important;
}

.user-message-content ul li {
  list-style-type: disc !important;
}

.user-message-content ol li {
  list-style-type: decimal !important;
}

.ql-editor.ql-blank::before {
  color: #9ca3af;
  font-style: normal;
}

/* Fix for Quill editor in dark mode */
.dark .ql-snow .ql-stroke {
  stroke: #e0e0e0 !important;
}

.dark .ql-snow .ql-fill {
  fill: #e0e0e0 !important;
}

.dark .ql-snow .ql-picker {
  color: #e0e0e0 !important;
}

.dark .ql-snow .ql-picker-options {
  background-color: #1e1e1e !important;
  border-color: #333 !important;
}

/* Fix for Quill editor pointer events */
.ql-toolbar.ql-snow,
.ql-container.ql-snow {
  pointer-events: auto !important;
  touch-action: auto !important;
  position: relative !important;
}

.ql-editor {
  pointer-events: auto !important;
  touch-action: auto !important;
  cursor: text !important;
  position: relative !important;
  user-select: text !important;
  -webkit-user-select: text !important;
  -moz-user-select: text !important;
  -ms-user-select: text !important;
}

/* Fix for Quill toolbar in dark mode */
.dark .ql-snow .ql-picker-label {
  color: #e0e0e0 !important;
}

/* Fix for Quill dropdown options in dark mode */
.dark .ql-snow .ql-picker-options {
  background-color: #1e1e1e !important;
  border-color: #333 !important;
}

/* Fix for Quill dropdown options in light mode */
.ql-snow .ql-picker-options {
  background-color: white !important;
  border: 1px solid #ccc !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
}

/* Specific fix for header dropdown */
.ql-snow .ql-picker.ql-header {
  position: relative !important;
  z-index: 1000 !important;
  width: auto !important;
  min-width: 120px !important;
}

.ql-snow .ql-picker.ql-header .ql-picker-options {
  top: 100% !important;
  left: 0 !important;
  position: absolute !important;
  z-index: 9999 !important;
  transform: translateY(0) !important;
  margin-top: 5px !important;
  width: auto !important;
  min-width: 120px !important;
}

/* Fix for header dropdown items */
.ql-snow .ql-picker.ql-header .ql-picker-item {
  white-space: nowrap !important;
  padding: 5px 10px !important;
}

.ql-snow .ql-picker.ql-header .ql-picker-item[data-value=""]::before {
  content: "Normal" !important;
}

.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="1"]::before {
  content: "Heading 1" !important;
}

.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="2"]::before {
  content: "Heading 2" !important;
}

.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="3"]::before {
  content: "Heading 3" !important;
}

/* Fix for dropdown label */
.ql-snow .ql-picker.ql-header .ql-picker-label {
  padding-right: 20px !important;
  position: relative !important;
}

/* Fix for blank dropdown text */
.ql-snow .ql-picker.ql-header .ql-picker-label[data-value=""] {
  color: inherit !important;
}

.ql-snow .ql-picker.ql-header .ql-picker-label[data-value=""]:not(:empty) {
  display: inline-block !important;
}

.ql-snow .ql-picker.ql-header .ql-picker-label:empty::before {
  content: "Normal" !important;
  display: inline-block !important;
}

/* Always show "Normal" for the default (no heading) option */
.ql-snow .ql-picker.ql-header .ql-picker-label[data-value=""] span::before {
  content: "Normal" !important;
  display: inline-block !important;
}

/* Fix for dropdown items */
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value=""]::before {
  content: "Normal" !important;
}

/* Ensure the dropdown arrow is visible */
.ql-snow .ql-picker.ql-header .ql-picker-label::after {
  content: '' !important;
  position: absolute !important;
  right: 5px !important;
  top: 50% !important;
  transform: translateY(-50%) !important;
  border-left: 4px solid transparent !important;
  border-right: 4px solid transparent !important;
  border-top: 4px solid currentColor !important;
  pointer-events: none !important;
}

/* Ensure all pickers have proper stacking */
.ql-snow .ql-picker {
  position: relative !important;
  z-index: 100 !important;
}

/* Ensure editor wrapper has proper stacking */
.quill-editor-wrapper {
  isolation: isolate !important;
}

/* Fix for Quill toolbar container */
.ql-toolbar-container {
  position: relative !important;
  z-index: 1000 !important;
}

/* Specific styles for Guidia chat editor */
.guidia-chat-editor .ql-editor ul {
  list-style-type: disc !important;
  padding-left: 1.5rem !important;
  margin: 0.5rem 0 !important;
}

.guidia-chat-editor .ql-editor ol {
  list-style-type: decimal !important;
  padding-left: 1.5rem !important;
  margin: 0.5rem 0 !important;
}

.guidia-chat-editor .ql-editor li {
  display: list-item !important;
  padding-left: 0 !important;
  margin-left: 0 !important;
}

.guidia-chat-editor .ql-editor li::before {
  display: none !important;
}

/* Rich text editor container */
.rich-text-editor-container {
  position: relative;
  pointer-events: auto !important;
  isolation: isolate;
  touch-action: auto !important;
  cursor: text !important;
  user-select: text !important;
  -webkit-user-select: text !important;
  -moz-user-select: text !important;
  -ms-user-select: text !important;
}

/* Typing indicator styles removed */

/* Chat message content styling */
.chat-message-content {
  line-height: 1.5;
  overflow-wrap: break-word;
  word-wrap: break-word;
  word-break: break-word;
  max-width: 100%;
  white-space: normal;
}

.chat-message-content p {
  margin-bottom: 0.75rem;
}

.chat-message-content p:last-child {
  margin-bottom: 0;
}

.chat-message-content ul,
.chat-message-content ol {
  margin-left: 1.5rem;
  margin-bottom: 0.75rem;
}

.chat-message-content h1,
.chat-message-content h2,
.chat-message-content h3,
.chat-message-content h4 {
  margin-top: 1rem;
  margin-bottom: 0.5rem;
  font-weight: 600;
}

.chat-message-content blockquote {
  border-left: 3px solid #800020;
  padding-left: 1rem;
  margin-left: 0;
  margin-right: 0;
  font-style: italic;
}

.chat-message-content code {
  background-color: rgba(0, 0, 0, 0.1);
  padding: 0.2rem 0.4rem;
  border-radius: 3px;
  font-family: monospace;
}

.chat-message-content pre {
  background-color: rgba(0, 0, 0, 0.1);
  padding: 1rem;
  border-radius: 5px;
  overflow-x: auto;
}

.chat-message-content hr {
  border: 0;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  margin: 1rem 0;
}

/* Fix for prose content with gray background */
.prose * {
  background-color: transparent !important;
  color: #333 !important; /* Dark gray for better contrast */
  word-wrap: break-word !important;
  overflow-wrap: break-word !important;
  word-break: break-word !important;
  max-width: 100% !important;
  white-space: normal !important;
  overflow: visible !important;
}

/* Ensure text is visible on burgundy backgrounds in light mode */
.bg-brand .prose *, .bg-brand-light .prose *, .bg-brand-lighter .prose * {
  color: white !important;
}

/* Fix for user messages with burgundy background */
div[class*="bg-brand"] .prose * {
  color: white !important;
}

/* Dark mode support */
.dark .prose * {
  color: #e0e0e0 !important; /* Light gray for dark mode */
  word-wrap: break-word !important;
  overflow-wrap: break-word !important;
  word-break: break-word !important;
  max-width: 100% !important;
  white-space: normal !important;
  overflow: visible !important;
}

/* Chat message rich text styles */
.chat-message-content {
  font-size: 0.875rem;
  line-height: 1.5;
}

.chat-message-content p {
  margin-bottom: 0.5rem;
}

.chat-message-content p:last-child {
  margin-bottom: 0;
}

.chat-message-content ul,
.chat-message-content ol {
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
  padding-left: 1.5rem;
}

.chat-message-content ul li,
.chat-message-content ol li {
  margin-bottom: 0.25rem;
}

.chat-message-content a {
  color: hsl(var(--brand)) !important;
  text-decoration: underline;
}

.chat-message-content code {
  font-family: monospace;
  background-color: rgba(0, 0, 0, 0.05) !important;
  padding: 0.1rem 0.3rem;
  border-radius: 0.25rem;
  font-size: 0.875em;
}

.dark .chat-message-content code {
  background-color: rgba(255, 255, 255, 0.1) !important;
}

.chat-message-content pre {
  background-color: rgba(0, 0, 0, 0.05) !important;
  padding: 0.75rem;
  border-radius: 0.375rem;
  overflow-x: auto;
  margin: 0.75rem 0;
}

.dark .chat-message-content pre {
  background-color: rgba(255, 255, 255, 0.05) !important;
}

/* Guidia AI chat specific styles */
.guidia-ai-message .prose {
  max-width: none;
  font-size: 0.875rem;
}

.guidia-ai-message .prose strong {
  font-weight: 600;
}

.guidia-ai-message .prose em {
  font-style: italic;
}

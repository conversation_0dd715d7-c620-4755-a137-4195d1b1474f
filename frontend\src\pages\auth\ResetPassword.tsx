import { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { toast } from 'sonner';
import { API_URL } from '../../config';
import { Skeleton } from '@/components/ui/skeleton';

export function ResetPassword() {
  const { token } = useParams();
  const navigate = useNavigate();
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [isValidToken, setIsValidToken] = useState(false);
  const [tokenVerifying, setTokenVerifying] = useState(true);

  useEffect(() => {
    const verifyToken = async () => {
      if (!token) {
        setIsValidToken(false);
        setTokenVerifying(false);
        return;
      }

      try {
        const response = await fetch(`${API_URL}/auth/reset-password/verify-token`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ token })
        });

        const data = await response.json();

        if (!response.ok) {
          throw new Error(data.error || 'Invalid reset token');
        }

        setIsValidToken(true);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Invalid reset token');
        setIsValidToken(false);
      } finally {
        setTokenVerifying(false);
      }
    };

    verifyToken();
  }, [token]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (password !== confirmPassword) {
      setError('Passwords do not match');
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`${API_URL}/auth/reset-password`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ token, password })
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to reset password');
      }

      toast.success('Password reset successful!');
      setTimeout(() => {
        navigate('/auth/login');
      }, 2000);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to reset password');
    } finally {
      setLoading(false);
    }
  };

  if (tokenVerifying) {
    return (
      <div className="min-h-screen bg-white pt-16 flex items-center justify-center px-4">
        <div className="w-full max-w-sm px-4 sm:px-6">
          <Skeleton className="h-10 w-48 mb-8" />

          <div className="space-y-6">
            <div className="mb-4">
              <Skeleton className="h-5 w-32 mb-1" />
              <Skeleton className="h-10 w-full" />
            </div>

            <div className="mb-6">
              <Skeleton className="h-5 w-48 mb-1" />
              <Skeleton className="h-10 w-full" />
            </div>

            <Skeleton className="h-12 w-full rounded-md" />
          </div>
        </div>
      </div>
    );
  }

  if (!isValidToken) {
    return (
      <div className="min-h-screen bg-white pt-16 flex items-center justify-center px-4">
        <div className="w-full max-w-sm px-4 sm:px-6">
          <h1 className="text-3xl font-bold text-brand mb-8">Invalid Reset Link</h1>
          <p className="text-muted-foreground mb-8">
            This password reset link is invalid or has expired. Please request a new password reset.
          </p>
          <button
            type="button"
            onClick={() => navigate('/auth/forgot-password')}
            className="w-full bg-brand text-white py-3 rounded-md font-medium hover:bg-brand-dark transition-colors"
          >
            Request New Reset Link
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-white pt-16 flex items-center justify-center px-4">
      <div className="w-full max-w-sm px-4 sm:px-6">
        <h1 className="text-3xl font-bold text-brand mb-8">Reset Password</h1>
        <form onSubmit={handleSubmit}>
          {error && (
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
              {error}
            </div>
          )}
          <div className="mb-4">
            <label className="block text-sm font-medium text-foreground text-left mb-1">
              New Password<span className="text-brand">*</span>
            </label>
            <input
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              className="w-full px-4 py-3 rounded-md border border-border focus:outline-none focus:ring-2 focus:ring-[#800020]"
              required
            />
          </div>
          <div className="mb-6">
            <label className="block text-sm font-medium text-foreground text-left mb-1">
              Confirm Password<span className="text-brand">*</span>
            </label>
            <input
              type="password"
              value={confirmPassword}
              onChange={(e) => setConfirmPassword(e.target.value)}
              className="w-full px-4 py-3 rounded-md border border-border focus:outline-none focus:ring-2 focus:ring-[#800020]"
              required
            />
          </div>
          <button
            type="submit"
            disabled={loading}
            className="w-full bg-brand text-white py-3 rounded-md font-medium hover:bg-brand-dark transition-colors disabled:opacity-50"
          >
            {loading ? 'Resetting Password...' : 'Reset Password'}
          </button>
        </form>
      </div>
    </div>
  );
}

export default ResetPassword;











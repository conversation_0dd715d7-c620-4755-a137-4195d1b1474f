# Database configuration
DB_HOST=localhost
DB_USER=your_db_user
DB_PASSWORD=your_db_password
DB_NAME=guidia-web-db

# JWT configuration
JWT_SECRET=your_jwt_secret_key

# Email configuration
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your_email_password

# Azure Storage configuration
AZURE_STORAGE_CONNECTION_STRING=your_azure_storage_connection_string
AZURE_STORAGE_CONTAINER_NAME=your_azure_container_name

# Server configuration
PORT=3001
FRONTEND_URL=http://localhost:1030

# Firebase configuration
FIREBASE_DATABASE_URL=your_firebase_database_url

# Key Rotation Configuration
ENABLE_KEY_ROTATION=true
# Cron schedule for key rotation (default: midnight on the 1st of each month)
KEY_ROTATION_SCHEDULE=0 0 1 * *  # Default: midnight on the 1st of each month

# AI configuration
SAMBANOVA_API_KEY=your_sambanova_api_key
DEEPSEEK_API_KEY=your_deepseek_api_key
